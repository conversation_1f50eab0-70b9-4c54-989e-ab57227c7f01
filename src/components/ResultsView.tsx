'use client';

import { useState } from 'react';
import { Search, Download, Filter, Star, Clock, CheckSquare, Square, Trash2 } from 'lucide-react';
import { AppState, MatchResult, Contact } from '@/types';

interface ResultsViewProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

export default function ResultsView({ appState, updateAppState }: ResultsViewProps) {
  const [selectedContacts, setSelectedContacts] = useState<Set<string>>(new Set());
  const [sortBy, setSortBy] = useState<'score' | 'name' | 'company'>('score');
  const [filterMinScore, setFilterMinScore] = useState(0);

  // Only show results from actual AI processing - no mock data
  const results = appState.currentResults?.contacts || [];
  const filteredResults = results.filter(result => result.score >= filterMinScore);

  const toggleContactSelection = (contactId: string) => {
    const newSelected = new Set(selectedContacts);
    if (newSelected.has(contactId)) {
      newSelected.delete(contactId);
    } else {
      newSelected.add(contactId);
    }
    setSelectedContacts(newSelected);
  };

  const selectAll = () => {
    if (selectedContacts.size === filteredResults.length) {
      setSelectedContacts(new Set());
    } else {
      setSelectedContacts(new Set(filteredResults.map(r => r.contact.id)));
    }
  };

  const deleteSelected = () => {
    if (selectedContacts.size === 0) return;

    const selectedIds = Array.from(selectedContacts);
    const remainingResults = results.filter(r => !selectedIds.includes(r.contact.id));

    // Update the app state with the filtered results
    updateAppState({
      currentResults: appState.currentResults ? {
        ...appState.currentResults,
        contacts: remainingResults,
        totalMatches: remainingResults.length,
      } : null
    });

    // Clear selection
    setSelectedContacts(new Set());
  };

  const exportSelected = () => {
    const selectedResults = filteredResults.filter(r =>
      selectedContacts.has(r.contact.id)
    );

    // Create CSV content
    const headers = ['Name', 'Company', 'Title', 'Email', 'Phone', 'City', 'State', 'Match Score'];
    const csvContent = [
      headers.join(','),
      ...selectedResults.map(result => [
        result.contact.name || '',
        result.contact.company || '',
        result.contact.title || '',
        result.contact.email || '',
        result.contact.phone || '',
        result.contact.city || '',
        result.contact.state || '',
        result.score.toFixed(2),
      ].map(field => `"${field}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `filtered-contacts-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600 bg-green-100';
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getScoreStars = (score: number) => {
    const stars = Math.round(score * 5);
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">Search Results</h2>
            <p className="text-sm text-gray-600">
              {filteredResults.length} contacts found
              {appState.currentResults && (
                <span> • Query: "{appState.currentResults.query}"</span>
              )}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={deleteSelected}
              disabled={selectedContacts.size === 0}
              className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Trash2 size={16} />
              Delete ({selectedContacts.size})
            </button>
            <button
              onClick={exportSelected}
              disabled={selectedContacts.size === 0}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Download size={16} />
              Export ({selectedContacts.size})
            </button>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="flex items-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <Filter size={16} className="text-gray-500" />
            <label className="text-sm text-gray-600">Min Score:</label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={filterMinScore}
              onChange={(e) => setFilterMinScore(parseFloat(e.target.value))}
              className="w-20"
            />
            <span className="text-sm text-gray-600">{filterMinScore.toFixed(1)}</span>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-600">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm border rounded px-2 py-1"
            >
              <option value="score">Match Score</option>
              <option value="name">Name</option>
              <option value="company">Company</option>
            </select>
          </div>

          <button
            onClick={selectAll}
            className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800"
          >
            {selectedContacts.size === filteredResults.length ? (
              <CheckSquare size={16} />
            ) : (
              <Square size={16} />
            )}
            {selectedContacts.size === filteredResults.length ? 'Deselect All' : 'Select All'}
          </button>
        </div>
      </div>

      {/* Results List */}
      <div className="bg-white rounded-lg shadow-lg">
        {filteredResults.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Search className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-lg">No results found</p>
            <p className="text-sm">Try adjusting your filters or search query</p>
          </div>
        ) : (
          <div className="divide-y">
            {filteredResults.map((result) => (
              <div
                key={result.contact.id}
                className={`p-6 hover:bg-gray-50 transition-colors ${
                  selectedContacts.has(result.contact.id) ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <button
                      onClick={() => toggleContactSelection(result.contact.id)}
                      className="mt-1"
                    >
                      {selectedContacts.has(result.contact.id) ? (
                        <CheckSquare className="text-blue-600" size={20} />
                      ) : (
                        <Square className="text-gray-400" size={20} />
                      )}
                    </button>

                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">
                          {result.contact.name}
                        </h3>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(result.score)}`}>
                          {(result.score * 100).toFixed(0)}% match
                        </div>
                        <div className="flex items-center gap-1">
                          {getScoreStars(result.score)}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                        <div>
                          <strong>Company:</strong> {result.contact.company}
                        </div>
                        <div>
                          <strong>Title:</strong> {result.contact.title}
                        </div>
                        <div>
                          <strong>Email:</strong> {result.contact.email}
                        </div>
                        <div>
                          <strong>Location:</strong> {result.contact.city}, {result.contact.state}
                        </div>
                      </div>

                      <div className="text-sm">
                        <p className="text-gray-700 mb-2">{result.explanation}</p>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-500">Matched fields:</span>
                          {result.matchedFields.map((field) => (
                            <span
                              key={field}
                              className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs"
                            >
                              {field}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Query Performance */}
      {appState.currentResults && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="font-semibold text-gray-800 mb-3">Query Performance</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Clock size={16} className="text-gray-500" />
              <span>Execution Time: {appState.currentResults.executionTime}ms</span>
            </div>
            <div className="flex items-center gap-2">
              <Star size={16} className="text-gray-500" />
              <span>Avg Confidence: {(appState.currentResults.confidence * 100).toFixed(1)}%</span>
            </div>
            <div className="flex items-center gap-2">
              <Search size={16} className="text-gray-500" />
              <span>Total Matches: {appState.currentResults.totalMatches}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
