'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Bot, User, Loader2, AlertCircle } from 'lucide-react';
import { AppState, AIConfig, ChatMessage, ChatSession } from '@/types';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { ContactFilterAgent } from '@/agents/contactFilterAgent';
import { GeneralChatAgent } from '@/agents/generalChatAgent';

// Remove local ChatMessage interface since we're importing it from types

interface ChatInterfaceProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

// Generate stable IDs that won't change between server and client
let messageIdCounter = 1;
const generateMessageId = () => `msg-${messageIdCounter++}`;

export default function ChatInterface({ appState, updateAppState }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const [filterAgent] = useState(() => new ContactFilterAgent());
  const [generalAgent] = useState(() => new GeneralChatAgent());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Update AI agents with current configuration
  useEffect(() => {
    const currentConfig = appState.aiConfigs[appState.currentAIProvider];
    if (currentConfig && currentConfig.apiKey) {
      generalAgent.setAIConfig(currentConfig);
    }
  }, [appState.aiConfigs, appState.currentAIProvider, generalAgent]);

  // Initialize client-side only data
  useEffect(() => {
    setIsClient(true);

    // Load current chat session or create a new one
    if (appState.currentChatSession) {
      setMessages(appState.currentChatSession.messages);
    } else {
      const welcomeMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'assistant',
        content: 'Hello! I\'m your AI assistant. I can help you with:\n\n🔍 **Contact Search**: Find specific contacts using natural language (e.g., "Find all category managers from grocery chains in Western Canada")\n\n💬 **General Chat**: Have conversations about any topic you\'re interested in\n\nWhat would you like to do today?',
        timestamp: new Date(),
        isContactQuery: false,
      };
      setMessages([welcomeMessage]);
    }
  }, [appState.currentChatSession]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Save chat session
  const saveChatSession = useCallback((updatedMessages: ChatMessage[]) => {
    if (updatedMessages.length <= 1) return; // Don't save sessions with only welcome message

    const currentSession = appState.currentChatSession;
    const now = new Date();

    if (currentSession) {
      // Update existing session
      const updatedSession: ChatSession = {
        ...currentSession,
        messages: updatedMessages,
        lastUpdated: now,
      };

      const updatedHistory = appState.chatHistory.map(session =>
        session.id === currentSession.id ? updatedSession : session
      );

      updateAppState({
        currentChatSession: updatedSession,
        chatHistory: updatedHistory,
      });
    } else {
      // Create new session
      const firstUserMessage = updatedMessages.find(msg => msg.type === 'user');
      const title = firstUserMessage
        ? generalAgent.generateConversationTitle(firstUserMessage.content)
        : 'New Conversation';

      const newSession: ChatSession = {
        id: `session-${Date.now()}`,
        title,
        messages: updatedMessages,
        createdAt: now,
        lastUpdated: now,
      };

      updateAppState({
        currentChatSession: newSession,
        chatHistory: [newSession, ...appState.chatHistory],
      });
    }
  }, [appState.currentChatSession, appState.chatHistory, updateAppState]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isProcessing) return;

    const query = inputValue.trim();
    const isContactQuery = generalAgent.isContactQuery(query);

    const userMessage: ChatMessage = {
      id: generateMessageId(),
      type: 'user',
      content: query,
      timestamp: new Date(),
      isContactQuery,
    };

    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setInputValue('');
    setIsProcessing(true);
    setProgress(0);
    setProcessingStep('Initializing...');

    // Update global app state to show processing
    updateAppState({ isProcessing: true });

    try {
      let assistantMessage: ChatMessage;

      if (isContactQuery) {
        // Handle contact search queries
        if (appState.allContacts.length === 0) {
          setProcessingStep('No data available');
          await new Promise(resolve => setTimeout(resolve, 1000));

          assistantMessage = {
            id: generateMessageId(),
            type: 'assistant',
            content: `I understand you're looking for: "${query}". However, I don't see any contact data loaded yet.

Please upload your CSV contact files in the Data tab first, then I can perform actual AI-powered searches for you!

Once you have data loaded, I'll be able to:
1. Extract key entities from your query (companies, job titles, locations)
2. Apply intelligent matching algorithms
3. Rank results by relevance and confidence
4. Show you exactly why each contact matched`,
            timestamp: new Date(),
            isContactQuery: true,
          };
        } else {
          // Process contact search with progress updates
          setProcessingStep('Analyzing your query...');
          setProgress(20);
          await new Promise(resolve => setTimeout(resolve, 500));

          setProcessingStep('Extracting entities and keywords...');
          setProgress(40);
          await new Promise(resolve => setTimeout(resolve, 500));

          setProcessingStep('Searching through contacts...');
          setProgress(60);

          // Use the AI agent to process the query
          const results = await filterAgent.processQuery(query, appState.allContacts);

          setProcessingStep('Ranking and filtering results...');
          setProgress(80);
          await new Promise(resolve => setTimeout(resolve, 300));

          setProcessingStep('Finalizing results...');
          setProgress(100);

          let responseContent = '';
          if (results.length === 0) {
            responseContent = `I searched through ${appState.allContacts.length} contacts for "${query}" but didn't find any matches.

Try:
- Using different keywords
- Being more specific or more general
- Checking for typos
- Using alternative terms (e.g., "grocery" instead of "supermarket")`;
          } else {
            responseContent = `Found ${results.length} contacts matching "${query}":

${results.slice(0, 5).map((result, index) =>
  `${index + 1}. **${result.contact.name}** at ${result.contact.company}
   - ${result.contact.title}
   - ${result.contact.city}, ${result.contact.state}
   - Match: ${(result.score * 100).toFixed(0)}% (${result.explanation})`
).join('\n\n')}

${results.length > 5 ? `\n...and ${results.length - 5} more results.` : ''}

Check the Results tab to see all matches and export options!`;

            // Update the app state with the results
            updateAppState({
              currentResults: {
                contacts: results,
                totalMatches: results.length,
                query: query,
                executionTime: 1200, // Mock execution time
                confidence: results.length > 0 ? results.reduce((sum, r) => sum + r.score, 0) / results.length : 0,
              },
              activeTab: 'results', // Switch to results tab
            });
          }

          assistantMessage = {
            id: generateMessageId(),
            type: 'assistant',
            content: responseContent,
            timestamp: new Date(),
            isContactQuery: true,
            resultCount: results.length,
          };
        }
      } else {
        // Handle general chat queries
        setProcessingStep('Thinking...');
        setProgress(50);
        await new Promise(resolve => setTimeout(resolve, 800));

        setProcessingStep('Generating response...');
        setProgress(100);

        // Get conversation history for context
        const conversationHistory = messages
          .filter(msg => !msg.isContactQuery) // Only include general chat messages for context
          .slice(-6) // Last 6 messages for context
          .map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.content
          }));

        const response = await generalAgent.processGeneralChat(query, conversationHistory);

        assistantMessage = {
          id: generateMessageId(),
          type: 'assistant',
          content: response,
          timestamp: new Date(),
          isContactQuery: false,
        };
      }

      // Add assistant message and save session
      const finalMessages = [...updatedMessages, assistantMessage];
      setMessages(finalMessages);
      saveChatSession(finalMessages);
    } catch (error) {
      console.error('Error processing query:', error);
      setProcessingStep('Error occurred');
      setProgress(0);

      const errorMessage: ChatMessage = {
        id: generateMessageId(),
        type: 'assistant',
        content: `❌ Sorry, I encountered an error processing your request:

**Error Details:** ${error instanceof Error ? error.message : 'Unknown error'}

**Troubleshooting:**
- Check if your CSV data is properly loaded
- Try rephrasing your query
- Ensure your query contains searchable terms
- Contact support if the issue persists

Please try again with a different query.`,
        timestamp: new Date(),
        isContactQuery: false,
      };

      const finalMessages = [...updatedMessages, errorMessage];
      setMessages(finalMessages);
      saveChatSession(finalMessages);
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
      setProgress(0);

      // Update global app state to clear processing
      updateAppState({ isProcessing: false });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg h-[600px] flex flex-col">
      {/* Chat Header */}
      <div className="border-b p-4">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
          <Bot className="text-blue-500" size={24} />
          AI Assistant
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Chat with me or ask me to find contacts using natural language
        </p>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {!isClient ? (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner />
            <span className="ml-2 text-gray-600">Loading chat...</span>
          </div>
        ) : (
          messages.map((message) => (
          <div
            key={message.id}
            className={`flex items-start gap-3 ${
              message.type === 'user' ? 'flex-row-reverse' : 'flex-row'
            }`}
          >
            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
              message.type === 'user'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-600'
            }`}>
              {message.type === 'user' ? <User size={16} /> : <Bot size={16} />}
            </div>

            <div className={`max-w-[80%] ${
              message.type === 'user' ? 'text-right' : 'text-left'
            }`}>
              <div className={`inline-block p-3 rounded-lg ${
                message.type === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                <p className="whitespace-pre-wrap">{message.content}</p>
              </div>
              {isClient && (
                <p className="text-xs text-gray-500 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </p>
              )}
            </div>
          </div>
          ))
        )}

        {isProcessing && (
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center">
              <Bot size={16} />
            </div>
            <div className="bg-gray-100 text-gray-800 p-3 rounded-lg min-w-[300px]">
              <div className="flex items-center gap-2 mb-2">
                <Loader2 className="animate-spin" size={16} />
                <span className="text-sm font-medium">{processingStep}</span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <div className="text-xs text-gray-600">{progress}% complete</div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t p-4">
        <div className="flex gap-2">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything or search for contacts... (e.g., 'Find all category managers' or 'What is AI?')"
            className="flex-1 resize-none border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={2}
            disabled={isProcessing}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isProcessing}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isProcessing ? (
              <Loader2 className="animate-spin" size={16} />
            ) : (
              <Send size={16} />
            )}
          </button>
        </div>

        {appState.allContacts.length === 0 && (
          <p className="text-sm text-amber-600 mt-2 flex items-center gap-1">
            ⚠️ No contact data loaded. Upload CSV files in the Data tab to enable AI filtering.
          </p>
        )}
      </div>
    </div>
  );
}
