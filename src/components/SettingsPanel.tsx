'use client';

import { useState, useEffect } from 'react';
import { Settings, Save, RefreshCw, <PERSON>, Slide<PERSON>, Palette, <PERSON>, EyeOff } from 'lucide-react';
import { AppState, AIConfig } from '@/types';
import { testAIConnection } from '@/lib/aiProviders';
import { StorageManager, modelSuggestions } from '@/lib/storage';

interface SettingsPanelProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

export default function SettingsPanel({ appState, updateAppState }: SettingsPanelProps) {
  const [currentProvider, setCurrentProvider] = useState<string>(appState.currentAIProvider || 'openai');
  const [currentConfig, setCurrentConfig] = useState<AIConfig>(
    appState.aiConfigs[currentProvider] || {
      provider: currentProvider as any,
      apiKey: '',
      model: 'gpt-4',
      temperature: 0.3,
      maxTokens: 2000,
      baseURL: '',
    }
  );

  const [matchingSettings, setMatchingSettings] = useState(StorageManager.loadMatchingSettings());
  const [uiSettings, setUISettings] = useState(StorageManager.loadUISettings());
  const [showApiKeys, setShowApiKeys] = useState<{ [provider: string]: boolean }>({});
  const [testingConnection, setTestingConnection] = useState(false);

  // Sync with app state when provider changes
  useEffect(() => {
    if (appState.aiConfigs[currentProvider]) {
      setCurrentConfig(appState.aiConfigs[currentProvider]);
    }
  }, [currentProvider, appState.aiConfigs]);

  // Handle provider change
  const handleProviderChange = (provider: string) => {
    setCurrentProvider(provider);

    // Load config for this provider or create default
    const providerConfig = appState.aiConfigs[provider] || {
      provider: provider as any,
      apiKey: '',
      model: getDefaultModel(provider),
      temperature: 0.3,
      maxTokens: 2000,
      baseURL: provider === 'openrouter' ? 'https://openrouter.ai/api/v1' : '',
    };

    setCurrentConfig(providerConfig);

    // Update current provider in app state
    updateAppState({ currentAIProvider: provider });
  };

  const getDefaultModel = (provider: string): string => {
    const suggestions = modelSuggestions[provider as keyof typeof modelSuggestions];
    return suggestions?.[0] || 'gpt-4';
  };

  const handleConfigChange = (field: keyof AIConfig, value: any) => {
    const updatedConfig = { ...currentConfig, [field]: value };
    setCurrentConfig(updatedConfig);

    // Save immediately to app state and storage
    const updatedConfigs = {
      ...appState.aiConfigs,
      [currentProvider]: updatedConfig,
    };

    updateAppState({ aiConfigs: updatedConfigs });
  };

  const handleSaveSettings = () => {
    // Save matching and UI settings
    StorageManager.saveMatchingSettings(matchingSettings);
    StorageManager.saveUISettings(uiSettings);

    // Show success message
    updateAppState({ error: null });
    alert('Settings saved successfully!');
  };

  const handleTestConnection = async () => {
    if (!currentConfig.apiKey) {
      alert('Please enter an API key first');
      return;
    }

    try {
      setTestingConnection(true);
      const result = await testAIConnection(currentConfig);

      if (result.success) {
        alert(`✅ ${result.message}`);
      } else {
        alert(`❌ Connection failed: ${result.message}`);
      }
    } catch (error) {
      alert(`❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setTestingConnection(false);
    }
  };

  const toggleApiKeyVisibility = (provider: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [provider]: !prev[provider],
    }));
  };

  return (
    <div className="space-y-6">
      {/* AI Configuration */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <Key className="text-blue-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-800">AI Configuration</h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              AI Provider
            </label>
            <select
              value={currentProvider}
              onChange={(e) => handleProviderChange(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic (Claude)</option>
              <option value="openrouter">OpenRouter</option>
              <option value="gemini">Google Gemini</option>
              <option value="local">Local Model</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Each provider's settings are saved separately
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            {currentProvider === 'openrouter' ? (
              <input
                type="text"
                value={currentConfig.model}
                onChange={(e) => handleConfigChange('model', e.target.value)}
                placeholder="e.g., openai/gpt-4, anthropic/claude-3-sonnet"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            ) : (
              <div className="space-y-2">
                <select
                  value={currentConfig.model}
                  onChange={(e) => handleConfigChange('model', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {modelSuggestions[currentProvider as keyof typeof modelSuggestions]?.map((model, index) => (
                    <option key={`${currentProvider}-${model}-${index}`} value={model}>{model}</option>
                  ))}
                </select>
                <input
                  type="text"
                  value={currentConfig.model}
                  onChange={(e) => handleConfigChange('model', e.target.value)}
                  placeholder="Or enter custom model name"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
            )}
            {currentProvider === 'openrouter' && (
              <p className="text-xs text-gray-500 mt-1">
                Enter the model ID from <a href="https://openrouter.ai/models" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">OpenRouter models list</a>
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API Key
            </label>
            <div className="relative">
              <input
                type={showApiKeys[currentProvider] ? 'text' : 'password'}
                value={currentConfig.apiKey}
                onChange={(e) => handleConfigChange('apiKey', e.target.value)}
                placeholder={
                  currentProvider === 'openai' ? 'sk-...' :
                  currentProvider === 'anthropic' ? 'sk-ant-...' :
                  currentProvider === 'openrouter' ? 'sk-or-...' :
                  currentProvider === 'gemini' ? 'AIza...' :
                  'Enter your API key'
                }
                className="w-full border border-gray-300 rounded-lg px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={() => toggleApiKeyVisibility(currentProvider)}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
              >
                {showApiKeys[currentProvider] ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
            {currentProvider === 'openrouter' && (
              <p className="text-xs text-gray-500 mt-1">
                Get your API key from <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">OpenRouter Keys</a>
              </p>
            )}
            {currentProvider === 'gemini' && (
              <p className="text-xs text-gray-500 mt-1">
                Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Google AI Studio</a>
              </p>
            )}
            {currentProvider === 'anthropic' && (
              <p className="text-xs text-gray-500 mt-1">
                Get your API key from <a href="https://console.anthropic.com/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Anthropic Console</a>
              </p>
            )}
            {currentProvider === 'openai' && (
              <p className="text-xs text-gray-500 mt-1">
                Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">OpenAI Platform</a>
              </p>
            )}
          </div>

          {currentProvider === 'openrouter' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Base URL
              </label>
              <input
                type="text"
                value={currentConfig.baseURL || ''}
                onChange={(e) => handleConfigChange('baseURL', e.target.value)}
                placeholder="https://openrouter.ai/api/v1"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                OpenRouter API endpoint (default: https://openrouter.ai/api/v1)
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature: {currentConfig.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={currentConfig.temperature}
                onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Focused</span>
                <span>Creative</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens
              </label>
              <input
                type="number"
                value={currentConfig.maxTokens}
                onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value))}
                min="100"
                max="4000"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <button
            onClick={handleTestConnection}
            disabled={testingConnection}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={testingConnection ? 'animate-spin' : ''} size={16} />
            Test Connection
          </button>
        </div>
      </div>

      {/* Matching Settings */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <Sliders className="text-green-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-800">Matching Settings</h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fuzzy Match Threshold: {matchingSettings.fuzzyThreshold}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={matchingSettings.fuzzyThreshold}
              onChange={(e) => setMatchingSettings(prev => ({
                ...prev,
                fuzzyThreshold: parseFloat(e.target.value)
              }))}
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Lower values include more approximate matches
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Semantic Threshold: {matchingSettings.semanticThreshold}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={matchingSettings.semanticThreshold}
              onChange={(e) => setMatchingSettings(prev => ({
                ...prev,
                semanticThreshold: parseFloat(e.target.value)
              }))}
              className="w-full"
            />
            <p className="text-xs text-gray-500 mt-1">
              Minimum confidence for semantic similarity matches
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Results
            </label>
            <input
              type="number"
              value={matchingSettings.maxResults}
              onChange={(e) => setMatchingSettings(prev => ({
                ...prev,
                maxResults: parseInt(e.target.value)
              }))}
              min="10"
              max="1000"
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* UI Settings */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <Palette className="text-purple-500" size={20} />
          <h2 className="text-xl font-semibold text-gray-800">Interface Settings</h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Theme
            </label>
            <select
              value={uiSettings.theme}
              onChange={(e) => setUISettings(prev => ({ ...prev, theme: e.target.value as any }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Language
            </label>
            <select
              value={uiSettings.language}
              onChange={(e) => setUISettings(prev => ({ ...prev, language: e.target.value }))}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="en">English</option>
              <option value="fr">Français</option>
              <option value="es">Español</option>
            </select>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveSettings}
          className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
        >
          <Save size={16} />
          Save All Settings
        </button>
      </div>
    </div>
  );
}
