'use client';

import { useState } from 'react';
import { MessageSquare, Clock, Search, Trash2, Eye, User, Bot } from 'lucide-react';
import { AppState, ChatSession, ChatMessage } from '@/types';

interface ChatHistoryViewProps {
  appState: AppState;
  updateAppState: (updates: Partial<AppState>) => void;
}

export default function ChatHistoryView({ appState, updateAppState }: ChatHistoryViewProps) {
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredSessions = appState.chatHistory.filter(session =>
    session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    session.messages.some(msg => 
      msg.content.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const deleteSession = (sessionId: string) => {
    const updatedHistory = appState.chatHistory.filter(session => session.id !== sessionId);
    updateAppState({ chatHistory: updatedHistory });
    
    // If the deleted session was selected, clear selection
    if (selectedSession?.id === sessionId) {
      setSelectedSession(null);
    }
  };

  const loadSession = (session: ChatSession) => {
    updateAppState({ 
      currentChatSession: session,
      activeTab: 'chat'
    });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getSessionStats = (session: ChatSession) => {
    const userMessages = session.messages.filter(msg => msg.type === 'user').length;
    const contactQueries = session.messages.filter(msg => msg.isContactQuery).length;
    const totalResults = session.messages.reduce((sum, msg) => sum + (msg.resultCount || 0), 0);
    
    return { userMessages, contactQueries, totalResults };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
              <MessageSquare className="text-blue-500" size={24} />
              Chat History
            </h2>
            <p className="text-sm text-gray-600">
              {appState.chatHistory.length} conversation{appState.chatHistory.length !== 1 ? 's' : ''} saved
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sessions List */}
        <div className="bg-white rounded-lg shadow-lg">
          <div className="p-4 border-b">
            <h3 className="font-semibold text-gray-800">Conversations</h3>
          </div>
          
          <div className="max-h-[600px] overflow-y-auto">
            {filteredSessions.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <MessageSquare className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <p className="text-lg">No conversations found</p>
                <p className="text-sm">Start chatting to see your conversation history here</p>
              </div>
            ) : (
              <div className="divide-y">
                {filteredSessions.map((session) => {
                  const stats = getSessionStats(session);
                  return (
                    <div
                      key={session.id}
                      className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                        selectedSession?.id === session.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                      }`}
                      onClick={() => setSelectedSession(session)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 truncate">
                            {session.title}
                          </h4>
                          <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Clock size={12} />
                              {formatDate(session.lastUpdated)}
                            </span>
                            <span>{stats.userMessages} messages</span>
                            {stats.contactQueries > 0 && (
                              <span className="text-blue-600">
                                {stats.contactQueries} searches
                              </span>
                            )}
                            {stats.totalResults > 0 && (
                              <span className="text-green-600">
                                {stats.totalResults} results
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1 truncate">
                            {session.messages[session.messages.length - 1]?.content || 'No messages'}
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              loadSession(session);
                            }}
                            className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Load conversation"
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteSession(session.id);
                            }}
                            className="p-1 text-red-600 hover:bg-red-100 rounded"
                            title="Delete conversation"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Session Details */}
        <div className="bg-white rounded-lg shadow-lg">
          <div className="p-4 border-b">
            <h3 className="font-semibold text-gray-800">
              {selectedSession ? 'Conversation Details' : 'Select a Conversation'}
            </h3>
          </div>
          
          {selectedSession ? (
            <div className="max-h-[600px] overflow-y-auto p-4">
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2">{selectedSession.title}</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>Created: {formatDate(selectedSession.createdAt)}</p>
                  <p>Last updated: {formatDate(selectedSession.lastUpdated)}</p>
                  <p>Messages: {selectedSession.messages.length}</p>
                </div>
              </div>
              
              <div className="space-y-3">
                {selectedSession.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex items-start gap-3 ${
                      message.type === 'user' ? 'flex-row-reverse' : 'flex-row'
                    }`}
                  >
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                      message.type === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {message.type === 'user' ? <User size={12} /> : <Bot size={12} />}
                    </div>
                    
                    <div className={`max-w-[80%] ${
                      message.type === 'user' ? 'text-right' : 'text-left'
                    }`}>
                      <div className={`inline-block p-2 rounded-lg text-sm ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        <p className="whitespace-pre-wrap">{message.content}</p>
                        {message.isContactQuery && message.resultCount !== undefined && (
                          <p className="text-xs mt-1 opacity-75">
                            Found {message.resultCount} contacts
                          </p>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDate(message.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 pt-4 border-t">
                <button
                  onClick={() => loadSession(selectedSession)}
                  className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Continue This Conversation
                </button>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <p>Select a conversation to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
