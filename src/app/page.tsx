'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/Tabs';
import { MessageSquare, Database, Search, Settings, BarChart3, History } from 'lucide-react';
import ChatInterface from '@/components/ChatInterface';
import DataManagement from '@/components/DataManagement';
import ResultsView from '@/components/ResultsView';
import ChatHistoryView from '@/components/ChatHistoryView';
import SettingsPanel from '@/components/SettingsPanel';
import AnalyticsView from '@/components/AnalyticsView';
import { AppState } from '@/types';
import { StorageManager } from '@/lib/storage';

export default function Home() {
  const [appState, setAppState] = useState<AppState>({
    activeTab: 'chat',
    uploadedFiles: [],
    allContacts: [],
    currentResults: null,
    isProcessing: false,
    error: null,
    chatHistory: [],
    currentChatSession: null,
    aiConfigs: {},
    currentAIProvider: 'openai',
  });

  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from storage on app initialization
  useEffect(() => {
    const loadStoredData = () => {
      try {
        const aiConfigs = StorageManager.loadAIConfigs();
        const csvFiles = StorageManager.loadCSVFiles();
        const contacts = StorageManager.loadContacts();
        const chatHistory = StorageManager.loadChatHistory();

        setAppState(prev => ({
          ...prev,
          aiConfigs,
          uploadedFiles: csvFiles,
          allContacts: contacts,
          chatHistory,
        }));
      } catch (error) {
        console.error('Failed to load stored data:', error);
      } finally {
        setIsLoaded(true);
      }
    };

    loadStoredData();
  }, []);

  const updateAppState = (updates: Partial<AppState>) => {
    setAppState(prev => {
      const newState = { ...prev, ...updates };

      // Auto-save certain data to storage when it changes
      if (updates.aiConfigs) {
        StorageManager.saveAIConfigs(updates.aiConfigs);
      }
      if (updates.uploadedFiles) {
        StorageManager.saveCSVFiles(updates.uploadedFiles);
      }
      if (updates.allContacts) {
        StorageManager.saveContacts(updates.allContacts);
      }
      if (updates.chatHistory) {
        StorageManager.saveChatHistory(updates.chatHistory);
      }

      return newState;
    });
  };

  // Show loading state while data is being loaded
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Loading your data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            AI Contact Filtering System
          </h1>
          <p className="text-lg text-gray-600">
            Intelligent contact management with natural language filtering
          </p>
        </div>

        {/* Main Application */}
        <Tabs
          value={appState.activeTab}
          onValueChange={(value) => updateAppState({ activeTab: value as AppState['activeTab'] })}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-6 bg-white rounded-lg shadow-sm border gap-1 p-1">
            <TabsTrigger
              value="chat"
              className="flex items-center gap-2 px-4 py-3"
            >
              <MessageSquare size={18} />
              Chat
            </TabsTrigger>
            <TabsTrigger
              value="data"
              className="flex items-center gap-2 px-4 py-3"
            >
              <Database size={18} />
              Data
            </TabsTrigger>
            <TabsTrigger
              value="results"
              className="flex items-center gap-2 px-4 py-3"
            >
              <Search size={18} />
              Results
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="flex items-center gap-2 px-4 py-3"
            >
              <History size={18} />
              History
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="flex items-center gap-2 px-4 py-3"
            >
              <BarChart3 size={18} />
              Analytics
            </TabsTrigger>
            <TabsTrigger
              value="settings"
              className="flex items-center gap-2 px-4 py-3"
            >
              <Settings size={18} />
              Settings
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="chat" className="space-y-4">
              <ChatInterface
                appState={appState}
                updateAppState={updateAppState}
              />
            </TabsContent>

            <TabsContent value="data" className="space-y-4">
              <DataManagement
                appState={appState}
                updateAppState={updateAppState}
              />
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <ResultsView
                appState={appState}
                updateAppState={updateAppState}
              />
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <ChatHistoryView
                appState={appState}
                updateAppState={updateAppState}
              />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <AnalyticsView
                appState={appState}
                updateAppState={updateAppState}
              />
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <SettingsPanel
                appState={appState}
                updateAppState={updateAppState}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}
