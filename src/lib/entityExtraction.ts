import { ExtractedEntities } from '@/types';

/**
 * Extract entities from natural language queries
 * This is a rule-based implementation that could be enhanced with ML models
 */
export async function extractEntities(query: string): Promise<ExtractedEntities> {
  const normalizedQuery = query.toLowerCase().trim();
  
  const entities: ExtractedEntities = {
    companies: [],
    jobTitles: [],
    locations: [],
    industries: [],
    keywords: [],
  };

  // Extract companies
  entities.companies = extractCompanies(normalizedQuery);
  
  // Extract job titles
  entities.jobTitles = extractJobTitles(normalizedQuery);
  
  // Extract locations
  entities.locations = extractLocations(normalizedQuery);
  
  // Extract industries
  entities.industries = extractIndustries(normalizedQuery);
  
  // Extract general keywords
  entities.keywords = extractKeywords(normalizedQuery);
  
  return entities;
}

function extractCompanies(query: string): string[] {
  const companies: string[] = [];
  
  // Known company patterns
  const companyPatterns = [
    // Major Canadian retailers
    /\b(loblaws?|metro|sobeys?|superstore|no frills|fortinos|zehrs)\b/gi,
    /\b(walmart|costco|canadian tire|home depot|rona)\b/gi,
    /\b(shoppers drug mart|rexall|pharmasave)\b/gi,
    
    // Generic company indicators
    /\b(\w+)\s+(inc|ltd|corp|corporation|company|co|limited)\b/gi,
    /\b(the\s+)?(\w+)\s+(group|enterprises|solutions|systems)\b/gi,
  ];
  
  for (const pattern of companyPatterns) {
    const matches = query.match(pattern);
    if (matches) {
      companies.push(...matches.map(m => m.trim()));
    }
  }
  
  // Look for phrases like "from X" or "at Y"
  const fromAtPattern = /\b(?:from|at)\s+([a-z\s]+?)(?:\s+(?:in|and|or|,)|$)/gi;
  let match;
  while ((match = fromAtPattern.exec(query)) !== null) {
    const potential = match[1].trim();
    if (potential.length > 2 && potential.length < 50) {
      companies.push(potential);
    }
  }
  
  return [...new Set(companies)]; // Remove duplicates
}

function extractJobTitles(query: string): string[] {
  const jobTitles: string[] = [];
  
  // Common job title patterns
  const titlePatterns = [
    // Management roles
    /\b(category\s+manager|product\s+manager|brand\s+manager)\b/gi,
    /\b(general\s+manager|store\s+manager|regional\s+manager)\b/gi,
    /\b(director|supervisor|coordinator|specialist|analyst)\b/gi,
    /\b(head\s+of|chief|senior|junior|assistant)\s+\w+/gi,
    
    // Specific roles
    /\b(buyer|purchaser|procurement|sourcing)\b/gi,
    /\b(sales\s+rep|account\s+manager|business\s+development)\b/gi,
    /\b(marketing|operations|finance|hr|human\s+resources)\b/gi,
    
    // Industry-specific
    /\b(baker|chef|cook|cashier|clerk)\b/gi,
    /\b(pharmacist|technician|nurse)\b/gi,
  ];
  
  for (const pattern of titlePatterns) {
    const matches = query.match(pattern);
    if (matches) {
      jobTitles.push(...matches.map(m => m.trim()));
    }
  }
  
  // Look for "all X" patterns
  const allPattern = /\ball\s+([a-z\s]+?)(?:\s+(?:from|in|at|and|or|,)|$)/gi;
  let match;
  while ((match = allPattern.exec(query)) !== null) {
    const potential = match[1].trim();
    if (isLikelyJobTitle(potential)) {
      jobTitles.push(potential);
    }
  }
  
  return [...new Set(jobTitles)];
}

function extractLocations(query: string): string[] {
  const locations: string[] = [];
  
  // Canadian provinces and territories
  const provinces = [
    'alberta', 'ab', 'british columbia', 'bc', 'manitoba', 'mb',
    'new brunswick', 'nb', 'newfoundland', 'nl', 'northwest territories', 'nt',
    'nova scotia', 'ns', 'nunavut', 'nu', 'ontario', 'on',
    'prince edward island', 'pe', 'pei', 'quebec', 'qc', 'saskatchewan', 'sk',
    'yukon', 'yt'
  ];
  
  // Major Canadian cities
  const cities = [
    'toronto', 'montreal', 'vancouver', 'calgary', 'edmonton', 'ottawa',
    'winnipeg', 'quebec city', 'hamilton', 'kitchener', 'london', 'victoria',
    'halifax', 'oshawa', 'windsor', 'saskatoon', 'regina', 'sherbrooke',
    'barrie', 'kelowna', 'abbotsford', 'kingston', 'sudbury', 'saguenay'
  ];
  
  // Regional terms
  const regions = [
    'western canada', 'eastern canada', 'atlantic canada', 'maritimes',
    'prairies', 'gta', 'greater toronto area', 'lower mainland',
    'northern ontario', 'southern ontario'
  ];
  
  const allLocations = [...provinces, ...cities, ...regions];
  
  for (const location of allLocations) {
    const pattern = new RegExp(`\\b${location.replace(/\s+/g, '\\s+')}\\b`, 'gi');
    if (pattern.test(query)) {
      locations.push(location);
    }
  }
  
  // Look for "in X" patterns
  const inPattern = /\bin\s+([a-z\s]+?)(?:\s+(?:and|or|,)|$)/gi;
  let match;
  while ((match = inPattern.exec(query)) !== null) {
    const potential = match[1].trim();
    if (potential.length > 2 && potential.length < 30) {
      locations.push(potential);
    }
  }
  
  return [...new Set(locations)];
}

function extractIndustries(query: string): string[] {
  const industries: string[] = [];
  
  // Industry keywords
  const industryPatterns = [
    // Retail
    /\b(grocery|supermarket|retail|store|shop|mall)\b/gi,
    /\b(food\s+service|restaurant|hospitality|catering)\b/gi,
    /\b(pharmacy|drug\s+store|health|medical)\b/gi,
    
    // Manufacturing
    /\b(manufacturing|production|factory|plant)\b/gi,
    /\b(automotive|aerospace|electronics|technology)\b/gi,
    
    // Services
    /\b(consulting|professional\s+services|financial|banking)\b/gi,
    /\b(education|healthcare|government|non-profit)\b/gi,
    
    // Specific sectors
    /\b(bakery|bakeries|bread|pastry)\b/gi,
    /\b(meat|dairy|produce|frozen)\b/gi,
    /\b(construction|real\s+estate|logistics|transportation)\b/gi,
  ];
  
  for (const pattern of industryPatterns) {
    const matches = query.match(pattern);
    if (matches) {
      industries.push(...matches.map(m => m.trim()));
    }
  }
  
  return [...new Set(industries)];
}

function extractKeywords(query: string): string[] {
  const keywords: string[] = [];
  
  // Split into words and filter
  const words = query.split(/\s+/);
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
    'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
    'before', 'after', 'above', 'below', 'between', 'among', 'all', 'any',
    'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'only',
    'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just',
    'should', 'now', 'find', 'show', 'get', 'search', 'look', 'me', 'i'
  ]);
  
  for (const word of words) {
    const cleaned = word.toLowerCase().replace(/[^\w]/g, '');
    if (cleaned.length > 2 && !stopWords.has(cleaned)) {
      keywords.push(cleaned);
    }
  }
  
  return [...new Set(keywords)];
}

function isLikelyJobTitle(text: string): boolean {
  const jobTitleIndicators = [
    'manager', 'director', 'supervisor', 'coordinator', 'specialist',
    'analyst', 'representative', 'assistant', 'associate', 'executive',
    'officer', 'lead', 'head', 'chief', 'senior', 'junior'
  ];
  
  const words = text.toLowerCase().split(/\s+/);
  return words.some(word => jobTitleIndicators.includes(word));
}

/**
 * Enhanced entity extraction using context and patterns
 */
export function extractEntitiesWithContext(query: string, context?: {
  previousQueries?: string[];
  uploadedData?: any[];
}): Promise<ExtractedEntities> {
  // This could be enhanced to use context from previous queries
  // and patterns found in uploaded data
  return extractEntities(query);
}

/**
 * Validate and clean extracted entities
 */
export function validateEntities(entities: ExtractedEntities): ExtractedEntities {
  return {
    companies: entities.companies.filter(c => c.length > 1 && c.length < 100),
    jobTitles: entities.jobTitles.filter(t => t.length > 2 && t.length < 50),
    locations: entities.locations.filter(l => l.length > 1 && l.length < 50),
    industries: entities.industries.filter(i => i.length > 2 && i.length < 50),
    keywords: entities.keywords.filter(k => k.length > 2 && k.length < 30),
  };
}
