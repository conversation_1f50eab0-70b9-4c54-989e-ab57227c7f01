import { AIConfig, AppState, CSVFile, Contact, ChatSession } from '@/types';

// Storage keys
const STORAGE_KEYS = {
  AI_CONFIGS: 'csv-query-app-ai-configs',
  CSV_FILES: 'csv-query-app-csv-files',
  CONTACTS: 'csv-query-app-contacts',
  CHAT_HISTORY: 'csv-query-app-chat-history',
  MATCHING_SETTINGS: 'csv-query-app-matching-settings',
  UI_SETTINGS: 'csv-query-app-ui-settings',
} as const;

// AI Configuration Storage
export interface StoredAIConfigs {
  [provider: string]: AIConfig;
}

export const defaultAIConfigs: StoredAIConfigs = {
  openai: {
    provider: 'openai',
    apiKey: '',
    model: 'gpt-4',
    temperature: 0.3,
    maxTokens: 2000,
  },
  anthropic: {
    provider: 'anthropic',
    apiKey: '',
    model: 'claude-3-sonnet-20240229',
    temperature: 0.3,
    maxTokens: 2000,
  },
  openrouter: {
    provider: 'openrouter',
    apiKey: '',
    model: 'openai/gpt-4',
    temperature: 0.3,
    maxTokens: 2000,
    baseURL: 'https://openrouter.ai/api/v1',
  },
  gemini: {
    provider: 'gemini',
    apiKey: '',
    model: 'gemini-pro',
    temperature: 0.3,
    maxTokens: 2000,
  },
  local: {
    provider: 'local',
    apiKey: '',
    model: 'llama-2-7b',
    temperature: 0.3,
    maxTokens: 2000,
  },
};

// Model suggestions for each provider
export const modelSuggestions = {
  openai: [
    'gpt-4',
    'gpt-4-turbo',
    'gpt-3.5-turbo',
    'gpt-4o',
    'gpt-4o-mini'
  ],
  anthropic: [
    'claude-3-sonnet-20240229',
    'claude-3-opus-20240229',
    'claude-3-haiku-20240307',
    'claude-2.1',
    'claude-2.0'
  ],
  openrouter: [
    'openai/gpt-4',
    'openai/gpt-4-turbo',
    'anthropic/claude-3-sonnet',
    'anthropic/claude-3-opus',
    'meta-llama/llama-2-70b-chat',
    'mistralai/mixtral-8x7b-instruct'
  ],
  gemini: [
    'gemini-pro',
    'gemini-pro-vision',
    'gemini-1.5-pro',
    'gemini-1.5-flash'
  ],
  local: [
    'llama-2-7b',
    'llama-2-13b',
    'llama-2-70b',
    'mistral-7b',
    'codellama-7b'
  ]
};

// Storage utility functions
export class StorageManager {
  private static isClient = typeof window !== 'undefined';

  // AI Configuration methods
  static saveAIConfigs(configs: StoredAIConfigs): void {
    if (!this.isClient) return;
    try {
      localStorage.setItem(STORAGE_KEYS.AI_CONFIGS, JSON.stringify(configs));
    } catch (error) {
      console.error('Failed to save AI configs:', error);
    }
  }

  static loadAIConfigs(): StoredAIConfigs {
    if (!this.isClient) return defaultAIConfigs;
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.AI_CONFIGS);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Merge with defaults to ensure all providers are present
        return { ...defaultAIConfigs, ...parsed };
      }
    } catch (error) {
      console.error('Failed to load AI configs:', error);
    }
    return defaultAIConfigs;
  }

  static saveAIConfig(provider: string, config: AIConfig): void {
    const configs = this.loadAIConfigs();
    configs[provider] = config;
    this.saveAIConfigs(configs);
  }

  // CSV Files and Contacts methods
  static saveCSVFiles(files: CSVFile[]): void {
    if (!this.isClient) return;
    try {
      // Convert dates to strings for JSON serialization
      const serializable = files.map(file => ({
        ...file,
        uploadDate: file.uploadDate.toISOString(),
      }));
      localStorage.setItem(STORAGE_KEYS.CSV_FILES, JSON.stringify(serializable));
    } catch (error) {
      console.error('Failed to save CSV files:', error);
    }
  }

  static loadCSVFiles(): CSVFile[] {
    if (!this.isClient) return [];
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CSV_FILES);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert date strings back to Date objects
        return parsed.map((file: any) => ({
          ...file,
          uploadDate: new Date(file.uploadDate),
        }));
      }
    } catch (error) {
      console.error('Failed to load CSV files:', error);
    }
    return [];
  }

  static saveContacts(contacts: Contact[]): void {
    if (!this.isClient) return;
    try {
      localStorage.setItem(STORAGE_KEYS.CONTACTS, JSON.stringify(contacts));
    } catch (error) {
      console.error('Failed to save contacts:', error);
    }
  }

  static loadContacts(): Contact[] {
    if (!this.isClient) return [];
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CONTACTS);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load contacts:', error);
    }
    return [];
  }

  // Chat History methods
  static saveChatHistory(history: ChatSession[]): void {
    if (!this.isClient) return;
    try {
      // Convert dates to strings for JSON serialization
      const serializable = history.map(session => ({
        ...session,
        createdAt: session.createdAt.toISOString(),
        lastUpdated: session.lastUpdated.toISOString(),
        messages: session.messages.map(msg => ({
          ...msg,
          timestamp: msg.timestamp.toISOString(),
        })),
      }));
      localStorage.setItem(STORAGE_KEYS.CHAT_HISTORY, JSON.stringify(serializable));
    } catch (error) {
      console.error('Failed to save chat history:', error);
    }
  }

  static loadChatHistory(): ChatSession[] {
    if (!this.isClient) return [];
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.CHAT_HISTORY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert date strings back to Date objects
        return parsed.map((session: any) => ({
          ...session,
          createdAt: new Date(session.createdAt),
          lastUpdated: new Date(session.lastUpdated),
          messages: session.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }));
      }
    } catch (error) {
      console.error('Failed to load chat history:', error);
    }
    return [];
  }

  // Other settings methods
  static saveMatchingSettings(settings: any): void {
    if (!this.isClient) return;
    try {
      localStorage.setItem(STORAGE_KEYS.MATCHING_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save matching settings:', error);
    }
  }

  static loadMatchingSettings(): any {
    if (!this.isClient) return { fuzzyThreshold: 0.7, semanticThreshold: 0.8, maxResults: 50 };
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.MATCHING_SETTINGS);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load matching settings:', error);
    }
    return { fuzzyThreshold: 0.7, semanticThreshold: 0.8, maxResults: 50 };
  }

  static saveUISettings(settings: any): void {
    if (!this.isClient) return;
    try {
      localStorage.setItem(STORAGE_KEYS.UI_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save UI settings:', error);
    }
  }

  static loadUISettings(): any {
    if (!this.isClient) return { theme: 'light', language: 'en' };
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.UI_SETTINGS);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load UI settings:', error);
    }
    return { theme: 'light', language: 'en' };
  }

  // Clear all storage (for debugging/reset)
  static clearAll(): void {
    if (!this.isClient) return;
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  }
}
