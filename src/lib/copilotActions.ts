import { CopilotAction, Contact, MatchResult, FilterResult } from '@/types';
import { ContactFilterAgent } from '@/agents/contactFilterAgent';
import { fuseSearch } from '@/lib/matching';

// Initialize the contact filter agent
const filterAgent = new ContactFilterAgent();

/**
 * CopilotKit Actions for contact filtering
 */

export const filterByIndustryAction: CopilotAction = {
  name: 'filterByIndustry',
  description: 'Filter contacts by business type or industry',
  parameters: {
    industry: {
      type: 'string',
      description: 'The industry to filter by (e.g., "grocery", "retail", "healthcare")',
      required: true,
    },
    contacts: {
      type: 'array',
      description: 'Array of contacts to filter',
      required: true,
    },
  },
  handler: async ({ industry, contacts }: { industry: string; contacts: Contact[] }) => {
    const results = fuseSearch(
      contacts,
      industry,
      ['industry', 'company', 'title'],
      0.6
    );

    return {
      matches: results.slice(0, 20),
      total: results.length,
      query: `Industry: ${industry}`,
    };
  },
};

export const filterByLocationAction: CopilotAction = {
  name: 'filterByLocation',
  description: 'Find contacts in specific geographic areas',
  parameters: {
    location: {
      type: 'string',
      description: 'The location to filter by (e.g., "Toronto", "Alberta", "Western Canada")',
      required: true,
    },
    contacts: {
      type: 'array',
      description: 'Array of contacts to filter',
      required: true,
    },
  },
  handler: async ({ location, contacts }: { location: string; contacts: Contact[] }) => {
    const results = fuseSearch(
      contacts,
      location,
      ['city', 'state', 'address'],
      0.7
    );

    return {
      matches: results.slice(0, 20),
      total: results.length,
      query: `Location: ${location}`,
    };
  },
};

export const filterByCompanyAction: CopilotAction = {
  name: 'filterByCompany',
  description: 'Locate contacts from particular companies or competitors',
  parameters: {
    company: {
      type: 'string',
      description: 'The company name to filter by (e.g., "Loblaws", "Metro", "Sobeys")',
      required: true,
    },
    contacts: {
      type: 'array',
      description: 'Array of contacts to filter',
      required: true,
    },
  },
  handler: async ({ company, contacts }: { company: string; contacts: Contact[] }) => {
    const results = fuseSearch(
      contacts,
      company,
      ['company'],
      0.8
    );

    return {
      matches: results.slice(0, 20),
      total: results.length,
      query: `Company: ${company}`,
    };
  },
};

export const filterByRoleAction: CopilotAction = {
  name: 'filterByRole',
  description: 'Find contacts with specific job titles or seniority levels',
  parameters: {
    role: {
      type: 'string',
      description: 'The job title or role to filter by (e.g., "manager", "director", "category manager")',
      required: true,
    },
    contacts: {
      type: 'array',
      description: 'Array of contacts to filter',
      required: true,
    },
  },
  handler: async ({ role, contacts }: { role: string; contacts: Contact[] }) => {
    const results = fuseSearch(
      contacts,
      role,
      ['title'],
      0.7
    );

    return {
      matches: results.slice(0, 20),
      total: results.length,
      query: `Role: ${role}`,
    };
  },
};

export const combineFiltersAction: CopilotAction = {
  name: 'combineFilters',
  description: 'Apply multiple filtering criteria simultaneously using natural language',
  parameters: {
    query: {
      type: 'string',
      description: 'Natural language query combining multiple criteria (e.g., "category managers from grocery chains in Western Canada")',
      required: true,
    },
    contacts: {
      type: 'array',
      description: 'Array of contacts to filter',
      required: true,
    },
  },
  handler: async ({ query, contacts }: { query: string; contacts: Contact[] }) => {
    try {
      const results = await filterAgent.processQuery(query, contacts);
      
      return {
        matches: results.map(r => ({ item: r.contact, score: r.score })),
        total: results.length,
        query: query,
        details: results.map(r => ({
          contact: r.contact,
          score: r.score,
          explanation: r.explanation,
          matchedFields: r.matchedFields,
        })),
      };
    } catch (error) {
      console.error('Error in combineFilters:', error);
      return {
        matches: [],
        total: 0,
        query: query,
        error: 'Failed to process complex query',
      };
    }
  },
};

export const exportResultsAction: CopilotAction = {
  name: 'exportResults',
  description: 'Export filtered contact lists in various formats',
  parameters: {
    contacts: {
      type: 'array',
      description: 'Array of contacts to export',
      required: true,
    },
    format: {
      type: 'string',
      description: 'Export format: "csv", "json", or "xlsx"',
      required: false,
    },
    fields: {
      type: 'array',
      description: 'Specific fields to include in export',
      required: false,
    },
  },
  handler: async ({ 
    contacts, 
    format = 'csv', 
    fields = ['name', 'company', 'title', 'email', 'phone'] 
  }: { 
    contacts: Contact[]; 
    format?: string; 
    fields?: string[] 
  }) => {
    try {
      let exportData: string;
      
      switch (format.toLowerCase()) {
        case 'json':
          exportData = JSON.stringify(
            contacts.map(contact => 
              fields.reduce((obj, field) => {
                obj[field] = contact[field] || '';
                return obj;
              }, {} as any)
            ),
            null,
            2
          );
          break;
          
        case 'csv':
        default:
          const headers = fields.join(',');
          const rows = contacts.map(contact =>
            fields.map(field => `"${(contact[field] || '').toString().replace(/"/g, '""')}"`).join(',')
          );
          exportData = [headers, ...rows].join('\n');
          break;
      }
      
      // Create download
      const blob = new Blob([exportData], { 
        type: format === 'json' ? 'application/json' : 'text/csv' 
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `contacts-export-${new Date().toISOString().split('T')[0]}.${format}`;
      a.click();
      window.URL.revokeObjectURL(url);
      
      return {
        success: true,
        message: `Exported ${contacts.length} contacts as ${format.toUpperCase()}`,
        count: contacts.length,
        format: format,
      };
    } catch (error) {
      console.error('Export error:', error);
      return {
        success: false,
        message: 'Failed to export contacts',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
};

export const analyzeContactsAction: CopilotAction = {
  name: 'analyzeContacts',
  description: 'Provide insights about contact database composition',
  parameters: {
    contacts: {
      type: 'array',
      description: 'Array of contacts to analyze',
      required: true,
    },
  },
  handler: async ({ contacts }: { contacts: Contact[] }) => {
    try {
      // Industry analysis
      const industries = contacts.reduce((acc, contact) => {
        const industry = contact.industry || 'Unknown';
        acc[industry] = (acc[industry] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Location analysis
      const locations = contacts.reduce((acc, contact) => {
        const location = contact.city && contact.state 
          ? `${contact.city}, ${contact.state}`
          : contact.city || contact.state || 'Unknown';
        acc[location] = (acc[location] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Company analysis
      const companies = contacts.reduce((acc, contact) => {
        const company = contact.company || 'Unknown';
        acc[company] = (acc[company] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Job title analysis
      const titles = contacts.reduce((acc, contact) => {
        const title = contact.title || 'Unknown';
        acc[title] = (acc[title] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const topIndustries = Object.entries(industries)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([name, count]) => ({ name, count }));

      const topLocations = Object.entries(locations)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([name, count]) => ({ name, count }));

      const topCompanies = Object.entries(companies)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([name, count]) => ({ name, count }));

      const topTitles = Object.entries(titles)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([name, count]) => ({ name, count }));

      return {
        totalContacts: contacts.length,
        topIndustries,
        topLocations,
        topCompanies,
        topTitles,
        dataQuality: {
          hasEmail: contacts.filter(c => c.email).length,
          hasPhone: contacts.filter(c => c.phone).length,
          hasAddress: contacts.filter(c => c.address || (c.city && c.state)).length,
          hasCompany: contacts.filter(c => c.company).length,
          hasTitle: contacts.filter(c => c.title).length,
        },
        summary: `Database contains ${contacts.length} contacts across ${Object.keys(industries).length} industries and ${Object.keys(locations).length} locations.`,
      };
    } catch (error) {
      console.error('Analysis error:', error);
      return {
        error: 'Failed to analyze contacts',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
};

// Export all actions as an array for easy registration
export const copilotActions = [
  filterByIndustryAction,
  filterByLocationAction,
  filterByCompanyAction,
  filterByRoleAction,
  combineFiltersAction,
  exportResultsAction,
  analyzeContactsAction,
];

// Helper function to register actions with CopilotKit
export function registerCopilotActions() {
  return copilotActions;
}
