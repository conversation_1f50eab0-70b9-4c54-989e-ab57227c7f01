import Fuse from 'fuse.js';
import { metaphone } from 'metaphone';
import stringSimilarity from 'string-similarity';

/**
 * Exact string matching with case insensitivity
 */
export function exactMatch(text1: string, text2: string): number {
  const normalized1 = text1.toLowerCase().trim();
  const normalized2 = text2.toLowerCase().trim();

  if (normalized1 === normalized2) return 1.0;
  if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) return 0.8;

  return 0.0;
}

/**
 * Fuzzy string matching using multiple algorithms
 */
export function fuzzyMatch(text1: string, text2: string, threshold: number = 0.6): number {
  if (!text1 || !text2) return 0;

  const normalized1 = text1.toLowerCase().trim();
  const normalized2 = text2.toLowerCase().trim();

  // Exact match first
  if (normalized1 === normalized2) return 1.0;

  // Substring match
  if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
    const longer = normalized1.length > normalized2.length ? normalized1 : normalized2;
    const shorter = normalized1.length <= normalized2.length ? normalized1 : normalized2;
    return shorter.length / longer.length;
  }

  // String similarity
  const similarity = stringSimilarity.compareTwoStrings(normalized1, normalized2);

  return similarity >= threshold ? similarity : 0;
}

/**
 * Phonetic matching using Metaphone algorithm
 */
export function phoneticMatch(text1: string, text2: string): number {
  if (!text1 || !text2) return 0;

  const metaphone1 = metaphone(text1);
  const metaphone2 = metaphone(text2);

  if (metaphone1 === metaphone2) return 0.9;

  // Check if metaphones are similar (for partial matches)
  if (metaphone1 && metaphone2 && metaphone1.length > 1 && metaphone2.length > 1) {
    const similarity = stringSimilarity.compareTwoStrings(metaphone1, metaphone2);
    if (similarity > 0.7) return 0.7;
  }

  return 0;
}

/**
 * Semantic matching using embeddings (mock implementation)
 * In a real implementation, this would use actual embeddings
 */
export async function semanticMatch(text1: string, text2: string): Promise<number> {
  // Mock semantic similarity based on keyword overlap and context
  const words1 = text1.toLowerCase().split(/\s+/);
  const words2 = text2.toLowerCase().split(/\s+/);

  // Industry-specific synonyms
  const synonyms: Record<string, string[]> = {
    'grocery': ['supermarket', 'food', 'retail', 'store'],
    'manager': ['director', 'supervisor', 'lead', 'head'],
    'category': ['product', 'merchandise', 'buying'],
    'bakery': ['bread', 'pastry', 'baking', 'bakehouse'],
    'alberta': ['ab', 'calgary', 'edmonton'],
    'ontario': ['on', 'toronto', 'ottawa'],
    'british columbia': ['bc', 'vancouver', 'victoria'],
  };

  let matchScore = 0;
  let totalWords = Math.max(words1.length, words2.length);

  for (const word1 of words1) {
    for (const word2 of words2) {
      if (word1 === word2) {
        matchScore += 1;
      } else {
        // Check synonyms
        for (const [key, syns] of Object.entries(synonyms)) {
          if ((word1 === key && syns.includes(word2)) ||
              (word2 === key && syns.includes(word1)) ||
              (syns.includes(word1) && syns.includes(word2))) {
            matchScore += 0.8;
            break;
          }
        }
      }
    }
  }

  return Math.min(matchScore / totalWords, 1.0);
}

/**
 * Location-aware matching for geographic terms
 */
export function locationMatch(location1: string, location2: string): number {
  if (!location1 || !location2) return 0;

  const normalized1 = location1.toLowerCase().trim();
  const normalized2 = location2.toLowerCase().trim();

  // Exact match
  if (normalized1 === normalized2) return 1.0;

  // Province/state abbreviations
  const provinceMap: Record<string, string[]> = {
    'alberta': ['ab', 'alta'],
    'british columbia': ['bc', 'b.c.'],
    'ontario': ['on', 'ont'],
    'quebec': ['qc', 'que', 'pq'],
    'manitoba': ['mb', 'man'],
    'saskatchewan': ['sk', 'sask'],
    'nova scotia': ['ns', 'n.s.'],
    'new brunswick': ['nb', 'n.b.'],
    'newfoundland': ['nl', 'nf', 'nfld'],
    'prince edward island': ['pe', 'pei', 'p.e.i.'],
    'northwest territories': ['nt', 'nwt'],
    'nunavut': ['nu'],
    'yukon': ['yt', 'yuk'],
  };

  // Check province mappings
  for (const [province, abbreviations] of Object.entries(provinceMap)) {
    if ((normalized1 === province && abbreviations.includes(normalized2)) ||
        (normalized2 === province && abbreviations.includes(normalized1)) ||
        (abbreviations.includes(normalized1) && abbreviations.includes(normalized2))) {
      return 0.95;
    }
  }

  // City-province combinations
  const cityProvinceMap: Record<string, string[]> = {
    'toronto': ['ontario', 'on'],
    'vancouver': ['british columbia', 'bc'],
    'calgary': ['alberta', 'ab'],
    'edmonton': ['alberta', 'ab'],
    'montreal': ['quebec', 'qc'],
    'ottawa': ['ontario', 'on'],
    'winnipeg': ['manitoba', 'mb'],
    'halifax': ['nova scotia', 'ns'],
  };

  for (const [city, provinces] of Object.entries(cityProvinceMap)) {
    if ((normalized1.includes(city) && provinces.some(p => normalized2.includes(p))) ||
        (normalized2.includes(city) && provinces.some(p => normalized1.includes(p)))) {
      return 0.9;
    }
  }

  // Fuzzy match as fallback
  return fuzzyMatch(normalized1, normalized2, 0.7);
}

/**
 * Company name matching with common variations
 */
export function companyMatch(company1: string, company2: string): number {
  if (!company1 || !company2) return 0;

  const normalized1 = normalizeCompanyName(company1);
  const normalized2 = normalizeCompanyName(company2);

  // Exact match after normalization
  if (normalized1 === normalized2) return 1.0;

  // Check if one is contained in the other
  if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
    const longer = normalized1.length > normalized2.length ? normalized1 : normalized2;
    const shorter = normalized1.length <= normalized2.length ? normalized1 : normalized2;
    return Math.max(0.8, shorter.length / longer.length);
  }

  // Fuzzy match
  return fuzzyMatch(normalized1, normalized2, 0.7);
}

function normalizeCompanyName(company: string): string {
  return company
    .toLowerCase()
    .trim()
    // Remove common suffixes
    .replace(/\b(inc|ltd|llc|corp|corporation|company|co|limited)\b\.?/g, '')
    // Remove common prefixes
    .replace(/^(the\s+)/g, '')
    // Normalize spacing
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Multi-strategy matching that combines different approaches
 */
export async function multiMatch(
  text1: string,
  text2: string,
  strategies: ('exact' | 'fuzzy' | 'phonetic' | 'semantic')[] = ['exact', 'fuzzy', 'semantic']
): Promise<number> {
  const scores: number[] = [];

  for (const strategy of strategies) {
    switch (strategy) {
      case 'exact':
        scores.push(exactMatch(text1, text2));
        break;
      case 'fuzzy':
        scores.push(fuzzyMatch(text1, text2));
        break;
      case 'phonetic':
        scores.push(phoneticMatch(text1, text2));
        break;
      case 'semantic':
        scores.push(await semanticMatch(text1, text2));
        break;
    }
  }

  // Return the highest score from all strategies
  return Math.max(...scores);
}

/**
 * Fuse.js-based fuzzy search for multiple items
 */
export function fuseSearch<T>(
  items: T[],
  query: string,
  keys: string[],
  threshold: number = 0.6
): Array<{ item: T; score: number }> {
  const fuse = new Fuse(items, {
    keys,
    threshold: 1 - threshold, // Fuse uses distance, we use similarity
    includeScore: true,
    ignoreLocation: true,
    findAllMatches: true,
  });

  const results = fuse.search(query);

  return results.map(result => ({
    item: result.item,
    score: 1 - (result.score || 0), // Convert distance back to similarity
  }));
}
