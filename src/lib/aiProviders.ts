import { AIConfig, Contact, MatchResult, ExtractedEntities } from '@/types';

/**
 * Python Backend API Client for Pydantic AI agents
 */

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface ProcessQueryRequest {
  query: string;
  contacts: Contact[];
  ai_config?: AIConfig;
}

export interface ProcessQueryResponse {
  results: MatchResult[];
  query: string;
  total_matches: number;
}

export interface GeneralChatRequest {
  query: string;
  conversation_history?: Array<{ role: string; content: string }>;
  ai_config?: AIConfig;
}

export interface GeneralChatResponse {
  response: string;
  is_contact_query: boolean;
}

/**
 * Backend API Client for Pydantic AI agents
 */
class BackendAPIClient {
  private baseUrl: string;

  constructor(baseUrl: string = BACKEND_URL) {
    this.baseUrl = baseUrl;
  }

  async processContactQuery(request: ProcessQueryRequest): Promise<ProcessQueryResponse> {
    const response = await fetch(`${this.baseUrl}/api/agents/contact-filter/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.statusText}`);
    }

    return response.json();
  }

  async processGeneralChat(request: GeneralChatRequest): Promise<GeneralChatResponse> {
    const response = await fetch(`${this.baseUrl}/api/agents/general-chat/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`Backend API error: ${response.statusText}`);
    }

    return response.json();
  }

  async healthCheck(): Promise<{ status: string; agents: any; context7: string }> {
    const response = await fetch(`${this.baseUrl}/api/health`);

    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }

    return response.json();
  }
}

// Export singleton instance
export const backendAPI = new BackendAPIClient();

/**
 * Legacy support functions for backward compatibility
 * These now route to the Python backend instead of direct API calls
 */

// Legacy function for testing AI connection
export async function testAIConnection(config: AIConfig): Promise<{ success: boolean; message: string }> {
  try {
    const health = await backendAPI.healthCheck();
    return {
      success: true,
      message: `Backend connection successful! Status: ${health.status}, Context7: ${health.context7}`,
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

// Legacy function for entity extraction
export async function extractEntitiesWithAI(config: AIConfig, query: string): Promise<ExtractedEntities> {
  try {
    // This is now handled by the backend, but we provide a simple fallback
    return {
      companies: [],
      jobTitles: [],
      locations: [],
      industries: [],
      keywords: query.toLowerCase().split(' ').filter(word => word.length > 2),
    };
  } catch (error) {
    console.error('Error extracting entities:', error);
    return {
      companies: [],
      jobTitles: [],
      locations: [],
      industries: [],
      keywords: [],
    };
  }
}


