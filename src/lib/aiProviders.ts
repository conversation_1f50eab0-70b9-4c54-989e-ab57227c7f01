import { AIConfig } from '@/types';

/**
 * AI Provider integrations for OpenAI, Anthropic, OpenRouter, and Google Gemini
 */

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * OpenAI API integration
 */
export async function callOpenAI(config: AIConfig, messages: AIMessage[]): Promise<AIResponse> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
    },
    body: JSON.stringify({
      model: config.model,
      messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  return {
    content: data.choices[0]?.message?.content || '',
    usage: {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    },
  };
}

/**
 * Anthropic Claude API integration
 */
export async function callAnthropic(config: AIConfig, messages: AIMessage[]): Promise<AIResponse> {
  // Convert messages format for Anthropic
  const systemMessage = messages.find(m => m.role === 'system')?.content || '';
  const conversationMessages = messages.filter(m => m.role !== 'system');

  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': config.apiKey || '',
      'anthropic-version': '2023-06-01',
    },
    body: JSON.stringify({
      model: config.model,
      max_tokens: config.maxTokens,
      temperature: config.temperature,
      system: systemMessage,
      messages: conversationMessages,
    }),
  });

  if (!response.ok) {
    throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  return {
    content: data.content[0]?.text || '',
    usage: {
      promptTokens: data.usage?.input_tokens || 0,
      completionTokens: data.usage?.output_tokens || 0,
      totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0),
    },
  };
}

/**
 * OpenRouter API integration
 */
export async function callOpenRouter(config: AIConfig, messages: AIMessage[]): Promise<AIResponse> {
  const baseURL = config.baseURL || 'https://openrouter.ai/api/v1';
  
  const response = await fetch(`${baseURL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`,
      'HTTP-Referer': window.location.origin,
      'X-Title': 'AI Contact Filtering System',
    },
    body: JSON.stringify({
      model: config.model,
      messages,
      temperature: config.temperature,
      max_tokens: config.maxTokens,
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  return {
    content: data.choices[0]?.message?.content || '',
    usage: {
      promptTokens: data.usage?.prompt_tokens || 0,
      completionTokens: data.usage?.completion_tokens || 0,
      totalTokens: data.usage?.total_tokens || 0,
    },
  };
}

/**
 * Google Gemini API integration
 */
export async function callGemini(config: AIConfig, messages: AIMessage[]): Promise<AIResponse> {
  // Convert messages to Gemini format
  const contents = messages
    .filter(m => m.role !== 'system')
    .map(message => ({
      role: message.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: message.content }],
    }));

  // Add system message as the first user message if present
  const systemMessage = messages.find(m => m.role === 'system');
  if (systemMessage) {
    contents.unshift({
      role: 'user',
      parts: [{ text: `System: ${systemMessage.content}` }],
    });
  }

  const response = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents,
        generationConfig: {
          temperature: config.temperature,
          maxOutputTokens: config.maxTokens,
        },
      }),
    }
  );

  if (!response.ok) {
    throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  
  return {
    content: data.candidates[0]?.content?.parts[0]?.text || '',
    usage: {
      promptTokens: data.usageMetadata?.promptTokenCount || 0,
      completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
      totalTokens: data.usageMetadata?.totalTokenCount || 0,
    },
  };
}

/**
 * Universal AI client that routes to the appropriate provider
 */
export async function callAI(config: AIConfig, messages: AIMessage[]): Promise<AIResponse> {
  switch (config.provider) {
    case 'openai':
      return callOpenAI(config, messages);
    case 'anthropic':
      return callAnthropic(config, messages);
    case 'openrouter':
      return callOpenRouter(config, messages);
    case 'gemini':
      return callGemini(config, messages);
    case 'local':
      throw new Error('Local models not yet implemented');
    default:
      throw new Error(`Unknown AI provider: ${config.provider}`);
  }
}

/**
 * Test connection to AI provider
 */
export async function testAIConnection(config: AIConfig): Promise<{ success: boolean; message: string }> {
  try {
    const testMessages: AIMessage[] = [
      {
        role: 'user',
        content: 'Hello! Please respond with just "Connection successful" to test the API.',
      },
    ];

    const response = await callAI(config, testMessages);
    
    if (response.content.toLowerCase().includes('connection successful') || 
        response.content.toLowerCase().includes('hello') ||
        response.content.trim().length > 0) {
      return {
        success: true,
        message: `Connection successful! Model: ${config.model}, Response: "${response.content.slice(0, 50)}..."`,
      };
    } else {
      return {
        success: false,
        message: 'Connection established but unexpected response format.',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Extract entities using AI
 */
export async function extractEntitiesWithAI(config: AIConfig, query: string): Promise<{
  companies: string[];
  jobTitles: string[];
  locations: string[];
  industries: string[];
  keywords: string[];
}> {
  const messages: AIMessage[] = [
    {
      role: 'system',
      content: `You are an expert at extracting entities from contact filtering queries. 
Extract the following entities from the user's query and return them as a JSON object:
- companies: Array of company names mentioned
- jobTitles: Array of job titles or roles mentioned  
- locations: Array of locations (cities, provinces, regions) mentioned
- industries: Array of industries or business types mentioned
- keywords: Array of other relevant keywords

Return only valid JSON, no other text.`,
    },
    {
      role: 'user',
      content: query,
    },
  ];

  try {
    const response = await callAI(config, messages);
    const entities = JSON.parse(response.content);
    
    return {
      companies: entities.companies || [],
      jobTitles: entities.jobTitles || [],
      locations: entities.locations || [],
      industries: entities.industries || [],
      keywords: entities.keywords || [],
    };
  } catch (error) {
    console.error('Error extracting entities with AI:', error);
    // Fallback to empty entities
    return {
      companies: [],
      jobTitles: [],
      locations: [],
      industries: [],
      keywords: [],
    };
  }
}
