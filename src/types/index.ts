// Core contact data types
export interface Contact {
  id: string;
  name?: string;
  company?: string;
  title?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  industry?: string;
  website?: string;
  notes?: string;
  [key: string]: any; // Allow for dynamic fields from CSV
}

// CSV file management
export interface CSVFile {
  id: string;
  name: string;
  size: number;
  uploadDate: Date;
  headers: string[];
  rowCount: number;
  data: Contact[];
}

// Filtering and search types
export interface FilterCriteria {
  query: string;
  fields?: string[];
  industry?: string;
  location?: string;
  company?: string;
  role?: string;
}

export interface MatchResult {
  contact: Contact;
  score: number;
  matchedFields: string[];
  explanation: string;
}

export interface FilterResult {
  contacts: MatchResult[];
  totalMatches: number;
  query: string;
  executionTime: number;
  confidence: number;
}

// AI Agent types
export interface AgentState {
  query: string;
  extractedEntities: ExtractedEntities;
  searchStrategy: SearchStrategy;
  results: MatchResult[];
  confidence: number;
  step: AgentStep;
}

export interface ExtractedEntities {
  companies: string[];
  jobTitles: string[];
  locations: string[];
  industries: string[];
  keywords: string[];
}

export interface SearchStrategy {
  type: 'exact' | 'fuzzy' | 'semantic' | 'combined';
  fields: string[];
  weights: Record<string, number>;
  threshold: number;
}

export type AgentStep =
  | 'parsing'
  | 'entity_extraction'
  | 'strategy_generation'
  | 'searching'
  | 'ranking'
  | 'complete';

// CopilotKit action types
export interface CopilotAction {
  name: string;
  description: string;
  parameters: Record<string, any>;
  handler: (args: any) => Promise<any>;
}

// Chat history types
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isContactQuery?: boolean;
  resultCount?: number;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  lastUpdated: Date;
}

// UI state types
export interface AppState {
  activeTab: 'chat' | 'data' | 'results' | 'history' | 'settings' | 'analytics';
  uploadedFiles: CSVFile[];
  allContacts: Contact[];
  currentResults: FilterResult | null;
  isProcessing: boolean;
  error: string | null;
  chatHistory: ChatSession[];
  currentChatSession: ChatSession | null;
  aiConfigs: { [provider: string]: AIConfig };
  currentAIProvider: string;
}

// Export and analytics types
export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx';
  fields: string[];
  includeMatchScores: boolean;
}

export interface AnalyticsData {
  totalContacts: number;
  totalQueries: number;
  averageMatchScore: number;
  topIndustries: Array<{ name: string; count: number }>;
  topLocations: Array<{ name: string; count: number }>;
  queryHistory: Array<{ query: string; timestamp: Date; resultCount: number }>;
}

// Configuration types
export interface AIConfig {
  provider: 'openai' | 'anthropic' | 'openrouter' | 'gemini' | 'local';
  apiKey?: string;
  model: string;
  temperature: number;
  maxTokens: number;
  baseURL?: string; // For OpenRouter custom endpoint
}

export interface AppConfig {
  ai: AIConfig;
  matching: {
    fuzzyThreshold: number;
    semanticThreshold: number;
    maxResults: number;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
  };
}
