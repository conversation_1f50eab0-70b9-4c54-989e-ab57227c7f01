import { AIConfig } from '@/types';
import { callAI } from '@/lib/aiProviders';

// General AI chat agent for non-contact related conversations
export class GeneralChatAgent {
  private aiConfig?: AIConfig;

  constructor(aiConfig?: AIConfig) {
    this.aiConfig = aiConfig;
  }

  setAIConfig(config: AIConfig) {
    this.aiConfig = config;
  }

  // Detect if a query is contact-related or general chat
  public isContactQuery(query: string): boolean {
    const contactKeywords = [
      // Job titles
      'manager', 'director', 'specialist', 'buyer', 'coordinator', 'supervisor',
      'executive', 'analyst', 'representative', 'assistant', 'lead', 'head',
      
      // Industries
      'grocery', 'retail', 'bakery', 'food', 'restaurant', 'supermarket',
      'store', 'chain', 'company', 'business', 'corporation',
      
      // Locations
      'alberta', 'ontario', 'bc', 'toronto', 'vancouver', 'calgary', 'montreal',
      'canada', 'western', 'eastern', 'city', 'province', 'region',
      
      // Search terms
      'find', 'show', 'search', 'list', 'get', 'contacts', 'people',
      'employees', 'staff', 'team', 'department'
    ];

    const normalizedQuery = query.toLowerCase();
    return contactKeywords.some(keyword => normalizedQuery.includes(keyword));
  }

  // Process general chat queries using AI
  public async processGeneralChat(query: string, conversationHistory: Array<{role: string, content: string}> = []): Promise<string> {
    try {
      if (!this.aiConfig || !this.aiConfig.apiKey) {
        return this.getFallbackResponse(query);
      }

      const messages = [
        {
          role: 'system',
          content: `You are a helpful AI assistant integrated into a contact management system. You can have general conversations with users about any topic. Be friendly, informative, and helpful. 

If users ask about contact filtering or searching, politely redirect them to use specific contact search queries like "find managers in Alberta" or "show me bakeries in Toronto".

Keep responses conversational and engaging. You can discuss technology, business, general knowledge, or any other topics the user is interested in.`
        },
        ...conversationHistory,
        {
          role: 'user',
          content: query
        }
      ];

      const response = await callAI(this.aiConfig, messages);
      return response.content;

    } catch (error) {
      console.error('Error in general chat:', error);
      return this.getFallbackResponse(query);
    }
  }

  private getFallbackResponse(query: string): string {
    const fallbackResponses = [
      "I'd be happy to chat with you! However, I don't have access to external AI services right now. You can still use me to search through your contact database though!",
      
      "That's an interesting question! While I can't access external AI for general chat at the moment, I'm great at helping you find specific contacts in your database.",
      
      "I appreciate you wanting to chat! For now, I'm most helpful when you ask me to find specific contacts, like 'show me all managers in Alberta' or 'find bakeries in Toronto'.",
      
      "Thanks for the question! While my general chat capabilities are limited without AI configuration, I excel at contact searches. Try asking me to find specific people or companies!",
      
      "I'd love to help with that! Currently, I'm optimized for contact filtering and searching. You can ask me things like 'find all grocery managers' or 'show me contacts in Vancouver'."
    ];

    // Simple keyword-based response selection
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('hello') || lowerQuery.includes('hi') || lowerQuery.includes('hey')) {
      return "Hello! I'm your AI assistant. I can help you search through your contact database or have a general conversation. What would you like to do today?";
    }
    
    if (lowerQuery.includes('how are you') || lowerQuery.includes('how do you do')) {
      return "I'm doing great, thank you for asking! I'm here to help you manage your contacts and answer any questions you might have. How can I assist you today?";
    }
    
    if (lowerQuery.includes('what can you do') || lowerQuery.includes('help')) {
      return "I can help you in several ways:\n\n1. **Contact Search**: Find specific contacts using natural language (e.g., 'find all managers in Alberta')\n2. **General Chat**: Have conversations about various topics\n3. **Data Analysis**: Help you understand your contact database\n\nWhat would you like to explore?";
    }

    // Return a random fallback response
    return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
  }

  // Generate a conversation title based on the first user message
  public generateConversationTitle(firstMessage: string): string {
    const message = firstMessage.trim();
    
    // If it's a contact query, create a descriptive title
    if (this.isContactQuery(message)) {
      return `Contact Search: ${message.length > 30 ? message.substring(0, 30) + '...' : message}`;
    }
    
    // For general chat, create a friendly title
    if (message.length <= 40) {
      return message;
    }
    
    // Truncate long messages
    return message.substring(0, 37) + '...';
  }
}
