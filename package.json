{"name": "csv-query-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@copilotkit/react-core": "^1.8.13", "@copilotkit/react-textarea": "^1.8.13", "@copilotkit/react-ui": "^1.8.13", "@google/generative-ai": "^0.24.1", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/langgraph": "^0.2.73", "@langchain/openai": "^0.5.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "double-metaphone": "^2.0.1", "fuse.js": "^7.1.0", "lucide-react": "^0.511.0", "metaphone": "^2.0.1", "natural": "^8.1.0", "next": "15.1.8", "papaparse": "^5.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "string-similarity": "^4.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}