"""
Utility functions for fuzzy matching and text similarity
"""
from difflib import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import List, Tu<PERSON>

def fuzzy_match(text1: str, text2: str) -> float:
    """
    Calculate fuzzy match score between two strings
    Returns a score between 0.0 and 1.0
    """
    if not text1 or not text2:
        return 0.0
    
    # Normalize strings
    text1 = text1.lower().strip()
    text2 = text2.lower().strip()
    
    if text1 == text2:
        return 1.0
    
    # Use SequenceMatcher for similarity
    matcher = SequenceMatcher(None, text1, text2)
    return matcher.ratio()

def partial_match(text: str, pattern: str) -> float:
    """
    Check if pattern partially matches text
    Returns a score between 0.0 and 1.0
    """
    if not text or not pattern:
        return 0.0
    
    text = text.lower().strip()
    pattern = pattern.lower().strip()
    
    if pattern in text:
        return len(pattern) / len(text)
    
    # Check for word-level matches
    text_words = set(text.split())
    pattern_words = set(pattern.split())
    
    if pattern_words.issubset(text_words):
        return len(pattern_words) / len(text_words)
    
    # Check for partial word matches
    matches = 0
    for pattern_word in pattern_words:
        for text_word in text_words:
            if fuzzy_match(pattern_word, text_word) > 0.8:
                matches += 1
                break
    
    return matches / len(pattern_words) if pattern_words else 0.0

def find_best_matches(
    query: str, 
    candidates: List[str], 
    threshold: float = 0.3
) -> List[Tuple[str, float]]:
    """
    Find best matching candidates for a query
    Returns list of (candidate, score) tuples sorted by score
    """
    matches = []
    
    for candidate in candidates:
        score = fuzzy_match(query, candidate)
        if score >= threshold:
            matches.append((candidate, score))
    
    # Sort by score (highest first)
    matches.sort(key=lambda x: x[1], reverse=True)
    return matches

def normalize_text(text: str) -> str:
    """
    Normalize text for better matching
    """
    if not text:
        return ""
    
    # Convert to lowercase and strip whitespace
    text = text.lower().strip()
    
    # Remove extra whitespace
    text = " ".join(text.split())
    
    return text
