"""
Pydantic AI-powered Contact Filter Agent with Context7 integration
"""
import re
from typing import List, Optional
from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from models.types import Contact, MatchResult, ExtractedEntities, AIConfig
from services.context7_service import Context7Service
from utils.matching import fuzzy_match

class ContactFilterAgent:
    """AI-powered contact filtering agent using Pydantic AI"""
    
    def __init__(self):
        self.ai_config: Optional[AIConfig] = None
        self.context7_service = Context7Service()
        
        # Initialize Pydantic AI agent for entity extraction
        self.entity_extraction_agent = Agent(
            'openai:gpt-4o',
            output_type=ExtractedEntities,
            system_prompt="""
            You are an expert at extracting entities from contact filtering queries.
            Extract the following entities from the user's query:
            - companies: Array of company names mentioned
            - job_titles: Array of job titles or roles mentioned  
            - locations: Array of locations (cities, provinces, regions) mentioned
            - industries: Array of industries or business types mentioned
            - keywords: Array of other relevant keywords
            
            Be thorough and extract all relevant entities that could help filter contacts.
            """
        )
    
    def set_ai_config(self, config: AIConfig):
        """Set AI configuration for the agent"""
        self.ai_config = config
        
        # Update the agent model based on config
        model_name = f"{config.provider}:{config.model}"
        self.entity_extraction_agent = Agent(
            model_name,
            output_type=ExtractedEntities,
            system_prompt=self.entity_extraction_agent.system_prompt
        )
    
    async def process_query(self, query: str, contacts: List[Contact]) -> List[MatchResult]:
        """
        Process a contact filtering query using Pydantic AI
        """
        try:
            # Use Context7 to get relevant documentation if needed
            await self._enhance_with_context7(query)
            
            # Extract entities using Pydantic AI
            entities = await self._extract_entities_with_ai(query)
            
            # Perform contact matching
            results = []
            for contact in contacts:
                match_result = await self._match_contact(contact, query, entities)
                if match_result.score >= 0.3:  # Minimum threshold
                    results.append(match_result)
            
            # Sort by score (highest first)
            results.sort(key=lambda x: x.score, reverse=True)
            
            return results
            
        except Exception as e:
            print(f"Error processing query: {e}")
            # Fallback to simple matching
            return await self._fallback_simple_matching(query, contacts)
    
    async def _extract_entities_with_ai(self, query: str) -> ExtractedEntities:
        """Extract entities using Pydantic AI agent"""
        try:
            result = await self.entity_extraction_agent.run(query)
            return result.output
        except Exception as e:
            print(f"AI entity extraction failed: {e}")
            # Fallback to simple extraction
            return self._extract_simple_entities(query)
    
    def _extract_simple_entities(self, query: str) -> ExtractedEntities:
        """Simple rule-based entity extraction as fallback"""
        normalized_query = query.lower()
        
        # Known patterns for Canadian retail/grocery industry
        companies = []
        company_patterns = [
            r'\b(loblaws?|metro|sobeys?|superstore|walmart|costco|canadian tire)\b'
        ]
        for pattern in company_patterns:
            matches = re.findall(pattern, normalized_query)
            companies.extend(matches)
        
        job_titles = []
        title_patterns = [
            r'\b(manager|director|specialist|buyer|coordinator|supervisor|executive|analyst)\b'
        ]
        for pattern in title_patterns:
            matches = re.findall(pattern, normalized_query)
            job_titles.extend(matches)
        
        locations = []
        location_patterns = [
            r'\b(alberta|ontario|bc|toronto|vancouver|calgary|montreal|canada|western|eastern)\b'
        ]
        for pattern in location_patterns:
            matches = re.findall(pattern, normalized_query)
            locations.extend(matches)
        
        industries = []
        industry_patterns = [
            r'\b(grocery|retail|bakery|food|restaurant|supermarket)\b'
        ]
        for pattern in industry_patterns:
            matches = re.findall(pattern, normalized_query)
            industries.extend(matches)
        
        # Extract general keywords
        keywords = [word for word in normalized_query.split() if len(word) > 2]
        
        return ExtractedEntities(
            companies=list(set(companies)),
            job_titles=list(set(job_titles)),
            locations=list(set(locations)),
            industries=list(set(industries)),
            keywords=list(set(keywords))
        )
    
    async def _enhance_with_context7(self, query: str):
        """Use Context7 to enhance query understanding with up-to-date docs"""
        try:
            # Check if query mentions specific libraries or frameworks
            if any(term in query.lower() for term in ['pydantic', 'fastapi', 'ai', 'agent']):
                # Get relevant documentation
                docs = await self.context7_service.get_library_docs('pydantic-ai')
                # This documentation can be used to enhance the agent's understanding
                print(f"Enhanced with Context7 docs: {len(docs)} characters")
        except Exception as e:
            print(f"Context7 enhancement failed: {e}")
    
    async def _match_contact(self, contact: Contact, query: str, entities: ExtractedEntities) -> MatchResult:
        """Match a contact against the query and extracted entities"""
        total_score = 0.0
        match_count = 0
        matched_fields = []
        explanations = []
        
        # Check company matches
        if contact.company and entities.companies:
            for company in entities.companies:
                score = fuzzy_match(contact.company.lower(), company.lower())
                if score > 0.7:
                    total_score += score
                    match_count += 1
                    matched_fields.append('company')
                    explanations.append(f"Company: {int(score * 100)}% match")
                    break
        
        # Check job title matches
        if contact.title and entities.job_titles:
            for title in entities.job_titles:
                score = fuzzy_match(contact.title.lower(), title.lower())
                if score > 0.7:
                    total_score += score
                    match_count += 1
                    matched_fields.append('title')
                    explanations.append(f"Title: {int(score * 100)}% match")
                    break
        
        # Check location matches
        if (contact.city or contact.state) and entities.locations:
            location_text = f"{contact.city or ''} {contact.state or ''}".lower()
            for location in entities.locations:
                if location.lower() in location_text:
                    total_score += 0.8
                    match_count += 1
                    matched_fields.append('location')
                    explanations.append(f"Location: matches {location}")
                    break
        
        # Check industry matches
        if contact.industry and entities.industries:
            for industry in entities.industries:
                score = fuzzy_match(contact.industry.lower(), industry.lower())
                if score > 0.6:
                    total_score += score
                    match_count += 1
                    matched_fields.append('industry')
                    explanations.append(f"Industry: {int(score * 100)}% match")
                    break
        
        # Fallback: check if any field contains query keywords
        if match_count == 0:
            all_text = f"{contact.name or ''} {contact.company or ''} {contact.title or ''} {contact.industry or ''}".lower()
            query_words = [word for word in query.lower().split() if len(word) > 2]
            
            for word in query_words:
                if word in all_text:
                    total_score += 0.5
                    match_count += 1
                    matched_fields.append('general')
                    explanations.append(f"Contains keyword: {word}")
        
        final_score = total_score / match_count if match_count > 0 else 0.0
        explanation = ', '.join(explanations) if explanations else 'No matches found'
        
        return MatchResult(
            contact=contact,
            score=final_score,
            matched_fields=matched_fields,
            explanation=explanation
        )
    
    async def _fallback_simple_matching(self, query: str, contacts: List[Contact]) -> List[MatchResult]:
        """Simple fallback matching when AI fails"""
        results = []
        query_words = [word.lower() for word in query.split() if len(word) > 2]
        
        for contact in contacts:
            all_text = f"{contact.name or ''} {contact.company or ''} {contact.title or ''} {contact.industry or ''}".lower()
            
            matches = sum(1 for word in query_words if word in all_text)
            score = matches / len(query_words) if query_words else 0.0
            
            if score > 0.3:
                results.append(MatchResult(
                    contact=contact,
                    score=score,
                    matched_fields=['general'],
                    explanation=f"Simple text matching: {int(score * 100)}% relevance"
                ))
        
        results.sort(key=lambda x: x.score, reverse=True)
        return results
