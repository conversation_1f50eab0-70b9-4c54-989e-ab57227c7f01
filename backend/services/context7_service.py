"""
Context7 integration service for up-to-date library documentation
"""
import httpx
from typing import Optional, Dict, Any
import json

class Context7Service:
    """Service for integrating with Context7 MCP server"""
    
    def __init__(self, base_url: str = "http://localhost:3001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def resolve_library_id(self, library_name: str) -> Optional[str]:
        """
        Resolve a library name to a Context7-compatible library ID
        """
        try:
            response = await self.client.post(
                f"{self.base_url}/resolve-library-id",
                json={"libraryName": library_name}
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("library_id")
            else:
                print(f"Failed to resolve library ID for {library_name}: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Error resolving library ID: {e}")
            return None
    
    async def get_library_docs(
        self, 
        library_name: str, 
        topic: Optional[str] = None, 
        tokens: int = 10000
    ) -> str:
        """
        Get up-to-date documentation for a library using Context7
        """
        try:
            # First resolve the library ID
            library_id = await self.resolve_library_id(library_name)
            if not library_id:
                return f"Could not resolve library ID for {library_name}"
            
            # Get the documentation
            payload = {
                "context7CompatibleLibraryID": library_id,
                "tokens": tokens
            }
            
            if topic:
                payload["topic"] = topic
            
            response = await self.client.post(
                f"{self.base_url}/get-library-docs",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("documentation", "No documentation available")
            else:
                print(f"Failed to get docs for {library_name}: {response.status_code}")
                return f"Failed to retrieve documentation for {library_name}"
                
        except Exception as e:
            print(f"Error getting library docs: {e}")
            return f"Error retrieving documentation: {str(e)}"
    
    async def get_pydantic_ai_docs(self, topic: Optional[str] = None) -> str:
        """
        Get Pydantic AI documentation specifically
        """
        return await self.get_library_docs("pydantic-ai", topic)
    
    async def get_fastapi_docs(self, topic: Optional[str] = None) -> str:
        """
        Get FastAPI documentation specifically
        """
        return await self.get_library_docs("fastapi", topic)
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup when service is destroyed"""
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self.close())
        except:
            pass
