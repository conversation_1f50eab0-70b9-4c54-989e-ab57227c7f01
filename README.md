# AI-Powered Contact Filtering System

A comprehensive React-based contact management application that uses AI to intelligently filter messy contact data through natural language queries. Built with Next.js, TypeScript, LangGraph, and CopilotKit.

## Features

### 🤖 AI-Powered Filtering
- **Natural Language Queries**: Ask questions like "Find all category managers from grocery chains in Western Canada"
- **LangGraph Agent**: Multi-step workflow for query parsing, entity extraction, and intelligent matching
- **CopilotKit Integration**: Seamless AI interactions with chat interface and actions

### 📊 Intelligent Data Processing
- **Multi-Strategy Matching**: Exact, fuzzy, phonetic, and semantic similarity matching
- **Entity Extraction**: Automatically identifies companies, job titles, locations, and industries
- **Confidence Scoring**: Results ranked by relevance with explanations

### 📁 Data Management
- **CSV Upload**: Support for multiple file uploads with automatic header detection
- **Data Cleaning**: Intelligent normalization and handling of messy data
- **File Management**: Preview, merge, and manage multiple data sources

### 📈 Analytics & Insights
- **Performance Metrics**: Query success rates, response times, and user satisfaction
- **Data Composition**: Industry, location, and company breakdowns
- **Usage Statistics**: Query history and filtering patterns

### 🎯 Advanced Features
- **Bulk Operations**: Select and export filtered results
- **Multiple Export Formats**: CSV, JSON, and Excel support
- **Real-time Processing**: Non-blocking data processing with progress indicators
- **Responsive Design**: Modern UI with Tailwind CSS

## Technology Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **AI/ML**: LangGraph, CopilotKit, LangChain
- **AI Providers**: OpenAI, Anthropic Claude, OpenRouter, Google Gemini
- **Styling**: Tailwind CSS, Lucide React icons
- **Data Processing**: Papa Parse (CSV), Fuse.js (fuzzy search)
- **Matching Algorithms**: String similarity, Metaphone, custom location intelligence

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd csv-query-app
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Add your AI provider API keys:
```env
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
OPENROUTER_API_KEY=your_openrouter_key_here
GEMINI_API_KEY=your_gemini_key_here
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Usage

### 1. Upload Contact Data
- Navigate to the **Data** tab
- Drag and drop CSV files or click to select
- Preview uploaded data and manage files

### 2. AI-Powered Filtering
- Go to the **Chat** tab
- Ask natural language questions:
  - "Find bakeries in Alberta"
  - "Show me category managers from Superstore"
  - "List all contacts in Western Canada"

### 3. View and Export Results
- Check the **Results** tab for detailed filtering results
- Select contacts and export in various formats
- View match confidence scores and explanations

### 4. Analytics
- Monitor performance in the **Analytics** tab
- View data composition and usage statistics
- Track query history and success rates

### 5. AI Provider Configuration
- Go to the **Settings** tab to configure AI providers
- **OpenAI**: Use your OpenAI API key with models like GPT-4, GPT-4 Turbo
- **Anthropic**: Configure Claude 3 models (Opus, Sonnet, Haiku)
- **OpenRouter**: Access 100+ models with a single API key
  - Get API key from [OpenRouter Keys](https://openrouter.ai/keys)
  - Use model IDs like `openai/gpt-4`, `anthropic/claude-3-sonnet`
- **Google Gemini**: Use Gemini Pro and Gemini 1.5 models
  - Get API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
- Test connections and adjust matching thresholds

## Architecture

### LangGraph Agent Workflow
1. **Query Parsing**: Analyze natural language input
2. **Entity Extraction**: Identify companies, roles, locations, industries
3. **Strategy Generation**: Build dynamic search parameters
4. **Fuzzy Matching**: Apply multiple matching algorithms
5. **Result Ranking**: Sort by relevance and confidence

### CopilotKit Actions
- `filterByIndustry`: Filter by business type
- `filterByLocation`: Geographic filtering
- `filterByCompany`: Company-specific searches
- `filterByRole`: Job title filtering
- `combineFilters`: Complex multi-criteria queries
- `exportResults`: Data export functionality
- `analyzeContacts`: Database insights

## Development

### Project Structure
```
src/
├── app/                 # Next.js app router
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── ChatInterface.tsx
│   ├── DataManagement.tsx
│   ├── ResultsView.tsx
│   ├── SettingsPanel.tsx
│   └── AnalyticsView.tsx
├── agents/             # LangGraph agents
├── lib/                # Utility functions
│   ├── matching.ts     # Matching algorithms
│   ├── entityExtraction.ts
│   └── copilotActions.ts
└── types/              # TypeScript definitions
```

### Key Components
- **ContactFilterAgent**: Main LangGraph agent for query processing
- **Matching Library**: Multi-strategy text matching algorithms
- **Entity Extraction**: NLP for identifying query components
- **CopilotKit Actions**: AI-powered filtering operations

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, please open an issue on GitHub or contact the development team.
